import React, { useState, useCallback } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { 
  $getSelection, 
  $isRangeSelection, 
  FORMAT_TEXT_COMMAND, 
  FORMAT_ELEMENT_COMMAND,
  UNDO_COMMAND,
  REDO_COMMAND
} from 'lexical';
import { $createHeadingNode, $createQuoteNode } from '@lexical/rich-text';
import { INSERT_UNORDERED_LIST_COMMAND, INSERT_ORDERED_LIST_COMMAND } from '@lexical/list';
import { $createCodeNode } from '@lexical/code';
import { INSERT_TABLE_COMMAND } from '@lexical/table';
import MediaGallery from '../MediaGallery';
import LinkEditor from '../LinkEditor';
import { TableCreationModal } from './TablePlugin';

interface EnhancedToolbarProps {
  onImageUpload?: (file: File) => Promise<{ url: string; alt?: string }>;
  onInsertLink?: (url: string, text: string, title?: string) => void;
}

const EnhancedToolbar: React.FC<EnhancedToolbarProps> = ({
  onImageUpload,
  onInsertLink
}) => {
  const [editor] = useLexicalComposerContext();
  const [showMediaGallery, setShowMediaGallery] = useState(false);
  const [showLinkEditor, setShowLinkEditor] = useState(false);
  const [showTableModal, setShowTableModal] = useState(false);
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set());

  // Update active formats based on selection
  React.useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const formats = new Set<string>();
          if (selection.hasFormat('bold')) formats.add('bold');
          if (selection.hasFormat('italic')) formats.add('italic');
          if (selection.hasFormat('underline')) formats.add('underline');
          if (selection.hasFormat('strikethrough')) formats.add('strikethrough');
          if (selection.hasFormat('code')) formats.add('code');
          setActiveFormats(formats);
        }
      });
    });
  }, [editor]);

  const formatText = useCallback((format: string) => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  }, [editor]);

  const formatElement = useCallback((format: string) => {
    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, format);
  }, [editor]);

  const insertHeading = useCallback((headingSize: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6') => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const headingNode = $createHeadingNode(headingSize);
        selection.insertNodes([headingNode]);
      }
    });
  }, [editor]);

  const insertQuote = useCallback(() => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const quoteNode = $createQuoteNode();
        selection.insertNodes([quoteNode]);
      }
    });
  }, [editor]);

  const insertCodeBlock = useCallback(() => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const codeNode = $createCodeNode();
        selection.insertNodes([codeNode]);
      }
    });
  }, [editor]);

  const insertList = useCallback((listType: 'bullet' | 'number') => {
    if (listType === 'bullet') {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
    }
  }, [editor]);

  const handleUndo = useCallback(() => {
    editor.dispatchCommand(UNDO_COMMAND, undefined);
  }, [editor]);

  const handleRedo = useCallback(() => {
    editor.dispatchCommand(REDO_COMMAND, undefined);
  }, [editor]);

  const handleImageSelect = useCallback((imageUrl: string, alt?: string) => {
    editor.dispatchCommand('INSERT_IMAGE', { url: imageUrl, alt });
    setShowMediaGallery(false);
  }, [editor]);

  const handleLinkInsert = useCallback((url: string, text: string, title?: string) => {
    // Insert link logic here
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        // Create link node and insert
        const linkNode = document.createElement('a');
        linkNode.href = url;
        linkNode.textContent = text;
        if (title) linkNode.title = title;
        // This is simplified - in real implementation, use Lexical's link creation
      }
    });
    if (onInsertLink) {
      onInsertLink(url, text, title);
    }
  }, [editor, onInsertLink]);

  const handleTableCreate = useCallback((rows: number, columns: number) => {
    editor.dispatchCommand(INSERT_TABLE_COMMAND, { rows, columns });
  }, [editor]);

  const ToolbarButton: React.FC<{
    onClick: () => void;
    isActive?: boolean;
    title: string;
    children: React.ReactNode;
    disabled?: boolean;
  }> = ({ onClick, isActive, title, children, disabled }) => (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`
        flex items-center justify-center w-8 h-8 rounded border-none cursor-pointer text-sm font-medium transition-colors
        ${isActive 
          ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' 
          : 'bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
        }
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
    >
      {children}
    </button>
  );

  const Divider = () => (
    <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
  );

  return (
    <>
      <div className="flex items-center gap-1 p-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex-wrap">
        {/* Undo/Redo */}
        <ToolbarButton onClick={handleUndo} title="Undo">
          ↶
        </ToolbarButton>
        <ToolbarButton onClick={handleRedo} title="Redo">
          ↷
        </ToolbarButton>
        
        <Divider />

        {/* Text Formatting */}
        <ToolbarButton 
          onClick={() => formatText('bold')} 
          isActive={activeFormats.has('bold')}
          title="Bold"
        >
          <strong>B</strong>
        </ToolbarButton>
        <ToolbarButton 
          onClick={() => formatText('italic')} 
          isActive={activeFormats.has('italic')}
          title="Italic"
        >
          <em>I</em>
        </ToolbarButton>
        <ToolbarButton 
          onClick={() => formatText('underline')} 
          isActive={activeFormats.has('underline')}
          title="Underline"
        >
          <u>U</u>
        </ToolbarButton>
        <ToolbarButton 
          onClick={() => formatText('strikethrough')} 
          isActive={activeFormats.has('strikethrough')}
          title="Strikethrough"
        >
          <s>S</s>
        </ToolbarButton>
        <ToolbarButton 
          onClick={() => formatText('code')} 
          isActive={activeFormats.has('code')}
          title="Inline Code"
        >
          {'</>'}
        </ToolbarButton>

        <Divider />

        {/* Headings */}
        <select
          onChange={(e) => {
            const value = e.target.value;
            if (value && value !== 'paragraph') {
              insertHeading(value as any);
            }
          }}
          className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          defaultValue="paragraph"
        >
          <option value="paragraph">Paragraph</option>
          <option value="h1">Heading 1</option>
          <option value="h2">Heading 2</option>
          <option value="h3">Heading 3</option>
          <option value="h4">Heading 4</option>
          <option value="h5">Heading 5</option>
          <option value="h6">Heading 6</option>
        </select>

        <Divider />

        {/* Alignment */}
        <ToolbarButton onClick={() => formatElement('left')} title="Align Left">
          ⫷
        </ToolbarButton>
        <ToolbarButton onClick={() => formatElement('center')} title="Align Center">
          ≡
        </ToolbarButton>
        <ToolbarButton onClick={() => formatElement('right')} title="Align Right">
          ⫸
        </ToolbarButton>
        <ToolbarButton onClick={() => formatElement('justify')} title="Justify">
          ≣
        </ToolbarButton>

        <Divider />

        {/* Lists */}
        <ToolbarButton onClick={() => insertList('bullet')} title="Bullet List">
          •
        </ToolbarButton>
        <ToolbarButton onClick={() => insertList('number')} title="Numbered List">
          1.
        </ToolbarButton>

        <Divider />

        {/* Quote and Code Block */}
        <ToolbarButton onClick={insertQuote} title="Quote">
          "
        </ToolbarButton>
        <ToolbarButton onClick={insertCodeBlock} title="Code Block">
          {'{ }'}
        </ToolbarButton>

        <Divider />

        {/* Media and Links */}
        <ToolbarButton onClick={() => setShowMediaGallery(true)} title="Insert Image">
          🖼️
        </ToolbarButton>
        <ToolbarButton onClick={() => setShowLinkEditor(true)} title="Insert Link">
          🔗
        </ToolbarButton>
        <ToolbarButton onClick={() => setShowTableModal(true)} title="Insert Table">
          ⊞
        </ToolbarButton>
      </div>

      {/* Modals */}
      <MediaGallery
        isOpen={showMediaGallery}
        onClose={() => setShowMediaGallery(false)}
        onSelectImage={handleImageSelect}
      />

      <LinkEditor
        isOpen={showLinkEditor}
        onClose={() => setShowLinkEditor(false)}
        onInsertLink={handleLinkInsert}
      />

      <TableCreationModal
        isOpen={showTableModal}
        onClose={() => setShowTableModal(false)}
        onCreateTable={handleTableCreate}
      />
    </>
  );
};

export default EnhancedToolbar;
